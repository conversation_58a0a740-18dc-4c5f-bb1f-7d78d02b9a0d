# 拓展活动账单上传功能修改说明

## 问题描述
在账单上传功能中，拓展活动数据缺少必填字段，导致暂存时数据不完整。主要缺少的字段包括：
- `invoiceTempId`: 临时id, 用于关联发票表
- `statementTempId`: 临时id, 用于关联水单表  
- `billPersonNum`: 账单人数
- `billUnitPrice`: 账单单价
- `sourceId`: 上一版本id

## 修改内容

### 1. 添加拓展活动数据映射函数 (billUploadschemePlan.vue)

在 `billUploadschemePlan.vue` 中添加了 `mapActivityDataForBackend` 函数：

```javascript
// 拓展活动数据映射函数
const mapActivityDataForBackend = (activityData: any) => {
  return activityData.map((item: any) => ({
    // 必填字段
    invoiceTempId: item.invoiceTempId, // 临时id, 用于关联发票表
    statementTempId: item.statementTempId, // 临时id, 用于关联水单表
    billPersonNum: item.billPersonNum || item.schemePersonNum, // 账单人数
    billUnitPrice: item.billUnitPrice, // 账单单价

    // 可选字段
    sourceId: item.sourceId, // 上一版本id
    miceDemandActivityId: item.miceDemandActivityId, // 需求拓展活动id
    miceSchemeActivityId: item.miceSchemeActivityId, // 方案拓展活动id
    demandDate: item.demandDate, // 需求日期
    demandUnitPrice: item.demandUnitPrice, // 费用标准
    schemePersonNum: item.schemePersonNum, // 方案人数
    schemeUnitPrice: item.schemeUnitPrice, // 方案单价
    description: item.description, // 方案说明
  }));
};
```

### 2. 更新数据提交函数

在 `schemePlanTempSave` 和 `SchemePlanSub` 函数中使用新的映射函数：

```javascript
// 原来
activities: [...activities],

// 修改后
activities: mapActivityDataForBackend([...activities]),
```

### 3. 修改拓展活动组件数据初始化 (billUploadschemeActivity.vue)

#### 3.1 更新数据映射逻辑
确保在数据初始化时设置所有必要字段的默认值：

```javascript
newSchemeList.value = demandData.map((e) => {
  return {
    // 必填字段
    invoiceTempId: e.invoiceTempId || null,
    statementTempId: e.statementTempId || null,
    billPersonNum: e.billPersonNum || e.personNum || e.schemePersonNum,
    billUnitPrice: e.billUnitPrice || e.demandUnitPrice || e.schemeUnitPrice,

    // 可选字段
    sourceId: e.sourceId || newObj.sourceId,
    miceDemandActivityId: e.id || e.miceDemandActivityId,
    miceSchemeActivityId: e.miceSchemeActivityId,
    demandDate: e.demandDate,
    demandUnitPrice: e.demandUnitPrice,
    schemePersonNum: e.personNum || e.schemePersonNum,
    schemeUnitPrice: e.demandUnitPrice || e.schemeUnitPrice,
    description: e.description,

    // 其他字段
    paths: e.paths,
    fileList: e.fileList,
  };
});
```

#### 3.2 更新价格计算函数
修改价格计算逻辑，使用账单相关字段：

```javascript
const priceCalcFun = () => {
  subtotal.value = 0;

  newSchemeList.value.forEach((e) => {
    if (e.billUnitPrice && e.billPersonNum) {
      subtotal.value += e.billUnitPrice * e.billPersonNum;
    }
  });

  emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};
```

#### 3.3 更新UI模板
- 将人数输入框改为使用 `billPersonNum`
- 添加账单单价输入框 `billUnitPrice`
- 更新总额计算显示

#### 3.4 更新校验函数
添加对必填字段的校验：

```javascript
const activitySub = () => {
  let isVerPassed = true;
  isVerifyFailed.value = false;

  newSchemeList.value.forEach((e, i) => {
    if (!e.billUnitPrice) {
      message.error('请输入' + e.demandDate + '拓展活动' + (i + 1) + '账单单价');
      isVerPassed = false;
      isVerifyFailed.value = true;
      return;
    }
    if (!e.billPersonNum) {
      message.error('请输入' + e.demandDate + '拓展活动' + (i + 1) + '账单人数');
      isVerPassed = false;
      isVerifyFailed.value = true;
      return;
    }
  });

  if (isVerPassed) {
    activityTempSave();
  }

  return isVerPassed;
};
```

## 修改后的数据结构

修改后，拓展活动数据将包含以下完整字段：

```javascript
{
  // 必填字段
  invoiceTempId: number | null,
  statementTempId: number | null,
  billPersonNum: number,
  billUnitPrice: number,
  
  // 可选字段
  sourceId: number,
  miceDemandActivityId: number,
  miceSchemeActivityId: number,
  demandDate: string,
  demandUnitPrice: number,
  schemePersonNum: number,
  schemeUnitPrice: number,
  description: string,
  
  // 其他字段
  paths: array,
  fileList: array
}
```

## 注意事项

1. `invoiceTempId` 和 `statementTempId` 可能需要从父组件或其他地方获取具体值
2. `sourceId` 优先使用顶层的 `newObj.sourceId`
3. 账单人数默认等于方案人数
4. 账单单价默认等于需求单价或方案单价
5. 添加了详细的调试日志，便于排查问题

## 测试建议

1. 测试数据初始化是否正确设置所有必填字段
2. 测试缓存数据的处理是否正常
3. 测试价格计算是否使用正确的字段
4. 测试校验功能是否正常工作
5. 测试数据映射函数是否正确转换数据格式
